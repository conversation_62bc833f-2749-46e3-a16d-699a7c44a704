"use client";

import {
  ActionBarPrimitive,
  BranchPickerPrimitive,
  ComposerPrimitive,
  MessagePrimitive,
  ThreadPrimitive,
} from "@assistant-ui/react";
import type { FC } from "react";
import {
  ArrowDownIcon,
  CheckIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  CopyIcon,
  PencilIcon,
  RefreshCwIcon,
  SendHorizontalIcon,
  MicIcon,
  MicOffIcon,
  PlayIcon,
  VolumeXIcon,
  Volume2Icon,
} from "lucide-react";
import { cn } from "@/lib/utils";

import { Button } from "@/components/ui/button";
import { MarkdownText } from "@/components/assistant-ui/markdown-text";
import { TooltipIconButton } from "@/components/assistant-ui/tooltip-icon-button";
import { AudioVisualizer, RecordingIndicator, AudioLevelMeter } from "@/components/ui/audio-visualizer";

interface RealtimeThreadProps {
  isRecording?: boolean;
  isMuted?: boolean;
  isPlaying?: boolean;
  playbackRate?: number;
  audioLevel?: number;
  onToggleRecording?: () => void;
  onToggleMute?: () => void;
  onTriggerResponse?: () => void;
  onPlaybackRateChange?: (rate: number) => void;
  connectionStatus?: 'connected' | 'connecting' | 'disconnected' | 'error';
}

export const RealtimeThread: FC<RealtimeThreadProps> = ({
  isRecording = false,
  isMuted = false,
  isPlaying = false,
  playbackRate = 1.0,
  audioLevel = 0,
  onToggleRecording,
  onToggleMute,
  onTriggerResponse,
  onPlaybackRateChange,
  connectionStatus = 'disconnected'
}) => {
  return (
    <ThreadPrimitive.Root
      className="aui-root aui-thread-root"
      style={{
        ["--thread-max-width" as string]: "48rem",
      }}
    >
      <ThreadPrimitive.Viewport className="aui-thread-viewport">
        <RealtimeWelcome
          connectionStatus={connectionStatus}
          isRecording={isRecording}
          isPlaying={isPlaying}
          audioLevel={audioLevel}
        />

        <ThreadPrimitive.Messages
          components={{
            UserMessage: UserMessage,
            EditComposer: EditComposer,
            AssistantMessage: AssistantMessage,
          }}
        />

        <ThreadPrimitive.If empty={false}>
          <div className="aui-thread-viewport-spacer" />
        </ThreadPrimitive.If>

        <div className="aui-thread-viewport-footer">
          <ThreadScrollToBottom />
          <RealtimeControls
            isRecording={isRecording}
            isMuted={isMuted}
            isPlaying={isPlaying}
            playbackRate={playbackRate}
            audioLevel={audioLevel}
            onToggleRecording={onToggleRecording}
            onToggleMute={onToggleMute}
            onTriggerResponse={onTriggerResponse}
            onPlaybackRateChange={onPlaybackRateChange}
          />
          <Composer />
        </div>
      </ThreadPrimitive.Viewport>
    </ThreadPrimitive.Root>
  );
};

const ThreadScrollToBottom: FC = () => {
  return (
    <ThreadPrimitive.ScrollToBottom asChild>
      <TooltipIconButton
        tooltip="Scroll to bottom"
        variant="outline"
        className="aui-thread-scroll-to-bottom"
      >
        <ArrowDownIcon />
      </TooltipIconButton>
    </ThreadPrimitive.ScrollToBottom>
  );
};

const RealtimeWelcome: FC<{
  connectionStatus: string;
  isRecording?: boolean;
  isPlaying?: boolean;
  audioLevel?: number;
}> = ({ connectionStatus, isRecording = false, isPlaying = false, audioLevel = 0 }) => {
  const getStatusMessage = () => {
    switch (connectionStatus) {
      case 'connected':
        return "Ready for your mock system design interview! Click 'Start session' to begin recording, then use 'Generate' to get AI feedback.";
      case 'connecting':
        return "Connecting to OpenAI Realtime API...";
      case 'error':
        return "Connection error. Please check your API key and try again.";
      default:
        return "Preparing your interview session...";
    }
  };

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return "text-green-600";
      case 'connecting':
        return "text-yellow-600";
      case 'error':
        return "text-red-600";
      default:
        return "text-slate-600";
    }
  };

  const getStatusIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return "🟢";
      case 'connecting':
        return "🟡";
      case 'error':
        return "🔴";
      default:
        return "⚪";
    }
  };

  return (
    <ThreadPrimitive.Empty>
      <div className="aui-thread-welcome-root">
        <div className="aui-thread-welcome-center max-w-2xl mx-auto text-center space-y-8">
          {/* Header */}
          <div className="space-y-4">
            <div className="flex items-center justify-center gap-3 mb-6">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-2xl">AI</span>
              </div>
              <div className="text-left">
                <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
                  Realtime Interview Assistant
                </h1>
                <p className="text-slate-600 font-medium">Powered by OpenAI Realtime API</p>
              </div>
            </div>

            {/* Status */}
            <div className="flex items-center justify-center gap-2 px-6 py-3 bg-slate-50 rounded-xl border border-slate-200 inline-flex">
              <span className="text-2xl">{getStatusIcon()}</span>
              <p className={cn("font-semibold", getStatusColor())}>
                {getStatusMessage()}
              </p>
            </div>
          </div>

          {/* Audio Visualization */}
          {connectionStatus === 'connected' && (
            <div className="space-y-6">
              <div className="flex items-center justify-center gap-6">
                <RecordingIndicator isRecording={isRecording} />
                <AudioVisualizer
                  isRecording={isRecording}
                  isPlaying={isPlaying}
                  audioLevel={audioLevel}
                  variant="waveform"
                  size="lg"
                  color={isRecording ? "red" : isPlaying ? "green" : "blue"}
                />
                {(isRecording || isPlaying) && (
                  <AudioLevelMeter level={audioLevel} />
                )}
              </div>

              {/* Quick tips */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <MicIcon className="w-4 h-4 text-blue-600" />
                  <span className="text-blue-700 font-medium">Press R to record</span>
                </div>
                <div className="flex items-center gap-2 p-3 bg-green-50 rounded-lg border border-green-200">
                  <SendHorizontalIcon className="w-4 h-4 text-green-600" />
                  <span className="text-green-700 font-medium">Press Space for AI</span>
                </div>
                <div className="flex items-center gap-2 p-3 bg-purple-50 rounded-lg border border-purple-200">
                  <Volume2Icon className="w-4 h-4 text-purple-600" />
                  <span className="text-purple-700 font-medium">Press M to mute</span>
                </div>
              </div>
            </div>
          )}
        </div>
        <RealtimeWelcomeSuggestions />
      </div>
    </ThreadPrimitive.Empty>
  );
};

const RealtimeWelcomeSuggestions: FC = () => {
  return (
    <div className="aui-thread-welcome-suggestions">
      <ThreadPrimitive.Suggestion
        className="aui-thread-welcome-suggestion"
        prompt="I'm ready to start the system design interview. Please begin with a question."
        method="replace"
        autoSend
      >
        <span className="aui-thread-welcome-suggestion-text">
          Start Interview
        </span>
      </ThreadPrimitive.Suggestion>
      <ThreadPrimitive.Suggestion
        className="aui-thread-welcome-suggestion"
        prompt="Can you explain how this realtime interview system works?"
        method="replace"
        autoSend
      >
        <span className="aui-thread-welcome-suggestion-text">
          How does this work?
        </span>
      </ThreadPrimitive.Suggestion>
    </div>
  );
};

interface RealtimeControlsProps {
  isRecording: boolean;
  isMuted: boolean;
  isPlaying: boolean;
  playbackRate: number;
  audioLevel?: number;
  onToggleRecording?: () => void;
  onToggleMute?: () => void;
  onTriggerResponse?: () => void;
  onPlaybackRateChange?: (rate: number) => void;
}

const RealtimeControls: FC<RealtimeControlsProps> = ({
  isRecording,
  isMuted,
  isPlaying,
  playbackRate,
  audioLevel = 0,
  onToggleRecording,
  onToggleMute,
  onTriggerResponse,
  onPlaybackRateChange
}) => {
  const playbackRates = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];

  return (
    <div className="flex items-center justify-between gap-4 p-4 bg-gradient-to-r from-slate-50 to-blue-50 rounded-2xl border border-slate-200/60 shadow-lg">
      {/* Left side - Audio visualization and recording */}
      <div className="flex items-center gap-4">
        {/* Audio Visualizer */}
        <AudioVisualizer
          isRecording={isRecording}
          isPlaying={isPlaying}
          audioLevel={audioLevel}
          variant="bars"
          size="sm"
          color={isRecording ? "red" : isPlaying ? "green" : "blue"}
        />

        {/* Recording Control */}
        <TooltipIconButton
          tooltip={isRecording ? "Stop Recording (R)" : "Start Recording (R)"}
          onClick={onToggleRecording}
          className={cn(
            "w-12 h-12 rounded-xl transition-all duration-200 transform hover:scale-105 shadow-md",
            isRecording
              ? "bg-gradient-to-r from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700 animate-pulse-recording"
              : "bg-gradient-to-r from-slate-100 to-slate-200 text-slate-600 hover:from-slate-200 hover:to-slate-300"
          )}
        >
          {isRecording ? <MicOffIcon className="w-6 h-6" /> : <MicIcon className="w-6 h-6" />}
        </TooltipIconButton>

        {/* Recording Indicator */}
        <RecordingIndicator isRecording={isRecording} />
      </div>

      {/* Center - Main controls */}
      <div className="flex items-center gap-3">
        {/* Trigger Response */}
        <TooltipIconButton
          tooltip="Trigger AI Response (Spacebar)"
          onClick={onTriggerResponse}
          className="bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 w-12 h-12 rounded-xl transition-all duration-200 transform hover:scale-105 shadow-md"
        >
          <SendHorizontalIcon className="w-6 h-6" />
        </TooltipIconButton>

        <div className="w-px h-8 bg-slate-300" />

        {/* Mute Control */}
        <TooltipIconButton
          tooltip={isMuted ? "Unmute (M)" : "Mute (M)"}
          onClick={onToggleMute}
          className={cn(
            "w-12 h-12 rounded-xl transition-all duration-200 transform hover:scale-105 shadow-md",
            isMuted
              ? "bg-gradient-to-r from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700"
              : "bg-gradient-to-r from-slate-100 to-slate-200 text-slate-600 hover:from-slate-200 hover:to-slate-300"
          )}
        >
          {isMuted ? <VolumeXIcon className="w-6 h-6" /> : <Volume2Icon className="w-6 h-6" />}
        </TooltipIconButton>
      </div>

      {/* Right side - Playback controls and status */}
      <div className="flex items-center gap-4">
        {/* Playback Rate */}
        <div className="flex items-center gap-2 px-3 py-2 bg-white rounded-xl border border-slate-200 shadow-sm">
          <span className="text-sm font-medium text-slate-700">Speed:</span>
          <select
            value={playbackRate}
            onChange={(e) => onPlaybackRateChange?.(parseFloat(e.target.value))}
            className="text-sm font-medium border-0 bg-transparent focus:outline-none focus:ring-2 focus:ring-blue-500 rounded px-2 py-1"
          >
            {playbackRates.map(rate => (
              <option key={rate} value={rate}>
                {rate}x
              </option>
            ))}
          </select>
        </div>

        {/* Audio Level Meter */}
        {(isRecording || isPlaying) && (
          <AudioLevelMeter level={audioLevel} />
        )}

        {/* Playing Indicator */}
        {isPlaying && (
          <div className="flex items-center gap-2 px-3 py-2 bg-green-50 rounded-xl border border-green-200">
            <PlayIcon className="w-4 h-4 text-green-600 animate-pulse" />
            <span className="text-sm font-medium text-green-700">Playing</span>
          </div>
        )}
      </div>
    </div>
  );
};

const Composer: FC = () => {
  return (
    <div className="mt-6">
      <ComposerPrimitive.Root className="relative bg-white rounded-2xl border border-slate-200 shadow-lg overflow-hidden">
        <div className="flex items-end gap-3 p-4">
          <div className="flex-1">
            <ComposerPrimitive.Input
              rows={1}
              autoFocus
              placeholder="Type a message or use voice controls..."
              className="w-full resize-none border-0 bg-transparent text-slate-900 placeholder-slate-500 focus:outline-none focus:ring-0 text-base leading-6 min-h-6 max-h-30"
            />
          </div>
          <ComposerAction />
        </div>

        {/* Typing indicator area */}
        <ThreadPrimitive.If running>
          <div className="px-4 pb-3">
            <TypingIndicator />
          </div>
        </ThreadPrimitive.If>
      </ComposerPrimitive.Root>
    </div>
  );
};

// Typing indicator component
const TypingIndicator: FC = () => {
  return (
    <div className="flex items-center gap-2 text-slate-500">
      <div className="flex gap-1">
        <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
        <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
        <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
      </div>
      <span className="text-sm">AI is thinking...</span>
    </div>
  );
};

const ComposerAction: FC = () => {
  return (
    <>
      <ThreadPrimitive.If running={false}>
        <ComposerPrimitive.Send asChild>
          <TooltipIconButton
            tooltip="Send message"
            className="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 rounded-xl transition-all duration-200 transform hover:scale-105 shadow-md"
          >
            <SendHorizontalIcon className="w-5 h-5" />
          </TooltipIconButton>
        </ComposerPrimitive.Send>
      </ThreadPrimitive.If>
      <ThreadPrimitive.If running>
        <ComposerPrimitive.Cancel asChild>
          <TooltipIconButton
            tooltip="Stop generation"
            className="w-10 h-10 bg-gradient-to-r from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700 rounded-xl transition-all duration-200 transform hover:scale-105 shadow-md"
          >
            <CircleStopIcon />
          </TooltipIconButton>
        </ComposerPrimitive.Cancel>
      </ThreadPrimitive.If>
    </>
  );
};

const UserMessage: FC = () => {
  return (
    <MessagePrimitive.Root className="group relative mb-6 animate-fade-in">
      <div className="flex items-start gap-4 max-w-4xl ml-auto">
        {/* Message content */}
        <div className="flex-1 max-w-2xl ml-auto">
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-2xl rounded-tr-md px-6 py-4 shadow-lg">
            <MessagePrimitive.Parts />
          </div>

          {/* Timestamp */}
          <div className="flex justify-end mt-2">
            <span className="text-xs text-slate-500">
              {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </span>
          </div>
        </div>

        {/* User avatar */}
        <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold shadow-md flex-shrink-0">
          U
        </div>
      </div>

      <UserActionBar />
      <BranchPicker className="aui-user-branch-picker" />
    </MessagePrimitive.Root>
  );
};

const UserActionBar: FC = () => {
  return (
    <ActionBarPrimitive.Root
      hideWhenRunning
      autohide="not-last"
      className="aui-user-action-bar-root"
    >
      <ActionBarPrimitive.Edit asChild>
        <TooltipIconButton tooltip="Edit">
          <PencilIcon />
        </TooltipIconButton>
      </ActionBarPrimitive.Edit>
    </ActionBarPrimitive.Root>
  );
};

const EditComposer: FC = () => {
  return (
    <ComposerPrimitive.Root className="aui-edit-composer-root">
      <ComposerPrimitive.Input className="aui-edit-composer-input" />

      <div className="aui-edit-composer-footer">
        <ComposerPrimitive.Cancel asChild>
          <Button variant="ghost">Cancel</Button>
        </ComposerPrimitive.Cancel>
        <ComposerPrimitive.Send asChild>
          <Button>Send</Button>
        </ComposerPrimitive.Send>
      </div>
    </ComposerPrimitive.Root>
  );
};

const AssistantMessage: FC = () => {
  return (
    <MessagePrimitive.Root className="group relative mb-6 animate-fade-in">
      <div className="flex items-start gap-4 max-w-4xl">
        {/* AI avatar */}
        <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center text-white font-semibold shadow-md flex-shrink-0">
          AI
        </div>

        {/* Message content */}
        <div className="flex-1 max-w-2xl">
          <div className="bg-white border border-slate-200 rounded-2xl rounded-tl-md px-6 py-4 shadow-lg">
            <MessagePrimitive.Parts components={{ Text: MarkdownText }} />
          </div>

          {/* Timestamp and status */}
          <div className="flex items-center justify-between mt-2">
            <span className="text-xs text-slate-500">
              AI Assistant
            </span>
            <span className="text-xs text-slate-500">
              {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </span>
          </div>
        </div>
      </div>

      <AssistantActionBar />
      <BranchPicker className="aui-assistant-branch-picker" />
    </MessagePrimitive.Root>
  );
};

const AssistantActionBar: FC = () => {
  return (
    <ActionBarPrimitive.Root
      hideWhenRunning
      autohide="not-last"
      autohideFloat="single-branch"
      className="aui-assistant-action-bar-root"
    >
      <ActionBarPrimitive.Copy asChild>
        <TooltipIconButton tooltip="Copy">
          <MessagePrimitive.If copied>
            <CheckIcon />
          </MessagePrimitive.If>
          <MessagePrimitive.If copied={false}>
            <CopyIcon />
          </MessagePrimitive.If>
        </TooltipIconButton>
      </ActionBarPrimitive.Copy>
      <ActionBarPrimitive.Reload asChild>
        <TooltipIconButton tooltip="Refresh">
          <RefreshCwIcon />
        </TooltipIconButton>
      </ActionBarPrimitive.Reload>
    </ActionBarPrimitive.Root>
  );
};

const BranchPicker: FC<BranchPickerPrimitive.Root.Props> = ({
  className,
  ...rest
}) => {
  return (
    <BranchPickerPrimitive.Root
      hideWhenSingleBranch
      className={cn("aui-branch-picker-root", className)}
      {...rest}
    >
      <BranchPickerPrimitive.Previous asChild>
        <TooltipIconButton tooltip="Previous">
          <ChevronLeftIcon />
        </TooltipIconButton>
      </BranchPickerPrimitive.Previous>
      <span className="aui-branch-picker-state">
        <BranchPickerPrimitive.Number /> / <BranchPickerPrimitive.Count />
      </span>
      <BranchPickerPrimitive.Next asChild>
        <TooltipIconButton tooltip="Next">
          <ChevronRightIcon />
        </TooltipIconButton>
      </BranchPickerPrimitive.Next>
    </BranchPickerPrimitive.Root>
  );
};

const CircleStopIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 16 16"
      fill="currentColor"
      width="16"
      height="16"
    >
      <rect width="10" height="10" x="3" y="3" rx="2" />
    </svg>
  );
};
