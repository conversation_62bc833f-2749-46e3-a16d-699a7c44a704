'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { useRealtimeWebSocket } from './useRealtimeWebSocket';
import { useAudioInput } from './useAudioInput';
import { useAudioOutput } from './useAudioOutput';
import { 
  createSessionConfig, 
  SessionConfigOptions, 
  createSessionMetrics, 
  updateSessionMetrics,
  SessionMetrics,
  isSessionExpired,
  shouldShowWarning,
  shouldReconnect,
  detectTriggerPhrase
} from '@/lib/session-config';
import { ConnectionState, RealtimeEvent } from '@/types/realtime';

interface UseRealtimeInterviewOptions {
  apiKey: string;
  sessionConfig?: SessionConfigOptions;
  autoConnect?: boolean;
  onError?: (error: Error) => void;
  onSessionExpired?: () => void;
  onWarning?: (message: string) => void;
}

interface UseRealtimeInterviewReturn {
  // Connection state
  connectionState: ConnectionState;
  isConnected: boolean;
  
  // Audio states
  isRecording: boolean;
  isPlaying: boolean;
  isMuted: boolean;
  playbackRate: number;
  
  // Session management
  sessionMetrics: SessionMetrics;
  isSessionExpired: boolean;
  shouldShowWarning: boolean;
  
  // Controls
  connect: () => Promise<void>;
  disconnect: () => void;
  startRecording: () => void;
  stopRecording: () => void;
  triggerResponse: () => void;
  setMuted: (muted: boolean) => void;
  setPlaybackRate: (rate: number) => void;
  
  // Status
  error: string | null;
}

export function useRealtimeInterview({
  apiKey,
  sessionConfig = {},
  autoConnect = false,
  onError,
  onSessionExpired,
  onWarning
}: UseRealtimeInterviewOptions): UseRealtimeInterviewReturn {
  const [sessionMetrics, setSessionMetrics] = useState<SessionMetrics>(createSessionMetrics());
  const [error, setError] = useState<string | null>(null);
  
  const sessionConfigRef = useRef(createSessionConfig(sessionConfig));
  const metricsIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const warningShownRef = useRef(false);
  const reconnectTriggeredRef = useRef(false);

  // WebSocket connection
  const {
    connectionState,
    connect: wsConnect,
    disconnect: wsDisconnect,
    appendAudioBuffer,
    commitAudioBuffer,
    createResponse,
    addEventListener,
    removeEventListener,
    reconnectWithHistory
  } = useRealtimeWebSocket({
    apiKey,
    autoConnect: autoConnect,
    sessionConfig: sessionConfigRef.current
  });

  // Audio input
  const {
    isRecording,
    startRecording: startAudioRecording,
    stopRecording: stopAudioRecording,
    initialize: initializeAudioInput
  } = useAudioInput({
    autoInitialize: false,
    onAudioData: (audioData) => {
      appendAudioBuffer(audioData);
      setSessionMetrics(prev => updateSessionMetrics(prev, {
        audioChunksProcessed: prev.audioChunksProcessed + 1
      }));
    },
    onError: (err) => {
      setError(err.message);
      onError?.(err);
    }
  });

  // Audio output
  const {
    isPlaying,
    isMuted,
    playbackRate,
    addAudioChunk,
    setMuted: setAudioMuted,
    setPlaybackRate: setAudioPlaybackRate,
    initialize: initializeAudioOutput
  } = useAudioOutput({
    autoInitialize: false,
    onError: (err) => {
      setError(err.message);
      onError?.(err);
    }
  });

  // Derived states
  const isConnected = connectionState === ConnectionState.CONNECTED;
  const isSessionExpiredState = isSessionExpired(sessionMetrics);
  const shouldShowWarningState = shouldShowWarning(sessionMetrics);

  const triggerResponse = useCallback(() => {
    if (!isConnected) {
      setError('Not connected to server');
      return;
    }

    commitAudioBuffer();
    createResponse();

    setSessionMetrics(prev => updateSessionMetrics(prev, {
      messagesExchanged: prev.messagesExchanged + 1
    }));
  }, [isConnected, commitAudioBuffer, createResponse]);

  // Event handlers
  useEffect(() => {
    if (!isConnected) return;

    const handleAudioDelta = (event: RealtimeEvent) => {
      if (event.type === 'response.audio.delta' && event.delta) {
        addAudioChunk(event.delta as string, event.item_id as string);
      }
    };

    const handleTranscriptDelta = (event: RealtimeEvent) => {
      if (event.type === 'response.audio_transcript.delta' && event.delta) {
        // Transcript delta received - could be used for real-time display
        console.log('Transcript delta:', event.delta);
      }
    };

    const handleInputTranscript = (event: RealtimeEvent) => {
      if (event.type === 'conversation.item.input_audio_transcription.completed') {
        const transcript = (event.transcript as string) || '';

        // Check for trigger phrases
        if (detectTriggerPhrase(transcript)) {
          triggerResponse();
        }
      }
    };

    const handleError = (event: RealtimeEvent) => {
      if (event.type === 'error') {
        const errorMessage = (event.error as { message?: string })?.message || 'Unknown error occurred';
        setError(errorMessage);
        onError?.(new Error(errorMessage));
      }
    };

    addEventListener('response.audio.delta', handleAudioDelta);
    addEventListener('response.audio_transcript.delta', handleTranscriptDelta);
    addEventListener('conversation.item.input_audio_transcription.completed', handleInputTranscript);
    addEventListener('error', handleError);

    return () => {
      removeEventListener('response.audio.delta', handleAudioDelta);
      removeEventListener('response.audio_transcript.delta', handleTranscriptDelta);
      removeEventListener('conversation.item.input_audio_transcription.completed', handleInputTranscript);
      removeEventListener('error', handleError);
    };
  }, [isConnected, addEventListener, removeEventListener, addAudioChunk, onError, triggerResponse]);

  const handleAutoReconnect = useCallback(async () => {
    try {
      await reconnectWithHistory();
      setSessionMetrics(prev => updateSessionMetrics(prev, {
        reconnectionCount: prev.reconnectionCount + 1
      }));
      reconnectTriggeredRef.current = false;
      warningShownRef.current = false;
    } catch (err) {
      console.error('Auto-reconnect failed:', err);
    }
  }, [reconnectWithHistory]);

  const disconnect = useCallback(() => {
    stopAudioRecording();
    wsDisconnect();

    if (metricsIntervalRef.current) {
      clearInterval(metricsIntervalRef.current);
      metricsIntervalRef.current = null;
    }
  }, [stopAudioRecording, wsDisconnect]);

  // Session monitoring
  useEffect(() => {
    if (isConnected) {
      metricsIntervalRef.current = setInterval(() => {
        setSessionMetrics(prev => {
          const updated = updateSessionMetrics(prev, {});

          // Check for warnings and expiration
          if (shouldShowWarning(updated) && !warningShownRef.current) {
            warningShownRef.current = true;
            onWarning?.('Session will expire in 5 minutes. Consider reconnecting soon.');
          }

          if (shouldReconnect(updated) && !reconnectTriggeredRef.current) {
            reconnectTriggeredRef.current = true;
            handleAutoReconnect();
          }

          if (isSessionExpired(updated)) {
            onSessionExpired?.();
            disconnect();
          }

          return updated;
        });
      }, 1000); // Update every second

      return () => {
        if (metricsIntervalRef.current) {
          clearInterval(metricsIntervalRef.current);
        }
      };
    }
  }, [isConnected, onWarning, onSessionExpired, disconnect, handleAutoReconnect]);

  const connect = useCallback(async () => {
    try {
      setError(null);
      
      // Initialize audio systems
      await Promise.all([
        initializeAudioInput(),
        initializeAudioOutput()
      ]);
      
      // Connect WebSocket
      await wsConnect();
      
      // Reset session metrics
      setSessionMetrics(createSessionMetrics());
      warningShownRef.current = false;
      reconnectTriggeredRef.current = false;
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to connect';
      setError(errorMessage);
      onError?.(err instanceof Error ? err : new Error(errorMessage));
    }
  }, [wsConnect, initializeAudioInput, initializeAudioOutput, onError]);

  const startRecording = useCallback(() => {
    if (!isConnected) {
      setError('Not connected to server');
      return;
    }
    
    startAudioRecording();
  }, [isConnected, startAudioRecording]);

  const stopRecording = useCallback(() => {
    stopAudioRecording();
  }, [stopAudioRecording]);

  const setMuted = useCallback((muted: boolean) => {
    setAudioMuted(muted);
  }, [setAudioMuted]);

  const setPlaybackRate = useCallback((rate: number) => {
    setAudioPlaybackRate(rate);
  }, [setAudioPlaybackRate]);

  // Auto-connect if enabled
  useEffect(() => {
    if (autoConnect && !isConnected && connectionState === ConnectionState.DISCONNECTED) {
      connect();
    }
  }, [autoConnect, isConnected, connectionState, connect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    connectionState,
    isConnected,
    isRecording,
    isPlaying,
    isMuted,
    playbackRate,
    sessionMetrics,
    isSessionExpired: isSessionExpiredState,
    shouldShowWarning: shouldShowWarningState,
    connect,
    disconnect,
    startRecording,
    stopRecording,
    triggerResponse,
    setMuted,
    setPlaybackRate,
    error
  };
}
